# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 18ms
    create-X86-model 10ms
    create-X86_64-model 20ms
    create-module-model 15ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 16ms
    create-X86-model 15ms
    create-X86_64-model 55ms
    create-module-model 15ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 32ms
    create-X86-model 34ms
    create-X86_64-model 86ms
  create-initial-cxx-model completed in 429ms
  [gap of 52ms]
create_cxx_tasks completed in 481ms

