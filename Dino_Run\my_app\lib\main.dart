import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Dino Run',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  late FlameGame game;

  @override
  void initState() {
    super.initState();
    game = FlameGame();
    _loadDinoSprite();
  }

  Future<void> _loadDinoSprite() async {
    final dinoSprite = await Sprite.load('DinoSprites_tard.gif');

    final dinoComponent = SpriteComponent(sprite: dinoSprite);
    dinoComponent.position = Vector2(200, 200);
    game.add(dinoComponent);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Center(
      child: GameWidget(game: game),
    ));
  }
}
