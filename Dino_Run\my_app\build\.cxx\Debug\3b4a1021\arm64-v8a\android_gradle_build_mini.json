{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\3b4a1021\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\3b4a1021\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}